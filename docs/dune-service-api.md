# Dune Service API Documentation

## 概述

Dune Service 是一个完整的服务，用于管理 Twitter 用户与 Dune 查询的关联关系，并提供定时数据抓取和查询功能。

## 功能特性

### 1. 数据库管理
- **Twitter-Dune 绑定管理**: 管理 Twitter 用户与 Dune 查询 ID 的关联关系
- **查询结果存储**: 存储每日的 Dune 查询结果数据
- **项目信息管理**: 自动获取和存储项目名称和 Logo

### 2. 定时数据抓取
- **每日定时执行**: 每天 UTC+8 上午 10 点自动执行
- **批量数据获取**: 遍历所有绑定的 Dune 查询 ID
- **错误重试机制**: 支持请求失败时的自动重试
- **增量更新**: 避免重复抓取同一天的数据

### 3. RESTful API
- **完整的 CRUD 操作**: 支持绑定关系的创建、读取、更新、删除
- **时间段查询**: 支持按时间范围查询项目数据
- **链 ID 过滤**: 支持按区块链 ID 过滤查询结果

## API 端点

### 绑定管理 API

#### 创建绑定
```
POST /api/dune/bindings
```

**请求体:**
```json
{
  "dune_query_id": "5399608",
  "twitter_user_name": "example_user",
  "chain_ids": ["1", "56", "137"],
  "project_name": "Example Project",
  "project_logo": "https://example.com/logo.png"
}
```

**响应:**
```json
{
  "status": "success",
  "binding": {
    "id": 1,
    "dune_query_id": "5399608",
    "twitter_user_name": "example_user",
    "chain_ids": ["1", "56", "137"],
    "project_name": "Example Project",
    "project_logo": "https://example.com/logo.png",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 获取所有绑定
```
GET /api/dune/bindings?limit=20&offset=0
```

**响应:**
```json
[
  {
    "id": 1,
    "dune_query_id": "5399608",
    "twitter_user_name": "example_user",
    "chain_ids": ["1", "56", "137"],
    "project_name": "Example Project",
    "project_logo": "https://example.com/logo.png",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 获取单个绑定
```
GET /api/dune/bindings/{id}
```

#### 更新绑定
```
PUT /api/dune/bindings/{id}
```

#### 删除绑定
```
DELETE /api/dune/bindings/{id}
```

### 项目数据查询 API

#### 获取项目数据
```
GET /api/dune/project-data
```

**查询参数:**
- `twitter_user_name` (必需): Twitter 用户名
- `start_date` (必需): 开始日期 (YYYY-MM-DD 格式)
- `end_date` (必需): 结束日期 (YYYY-MM-DD 格式)
- `chain_ids` (可选): 逗号分隔的区块链 ID 列表

**示例请求:**
```
GET /api/dune/project-data?twitter_user_name=example_user&start_date=2024-01-01&end_date=2024-01-31&chain_ids=1,56
```

**响应:**
```json
{
  "project_name": "Example Project",
  "project_logo": "https://example.com/logo.png",
  "twitter_user_name": "example_user",
  "query_results": {
    "5399608": [
      {
        "id": 1,
        "dune_query_id": "5399608",
        "query_date": "2024-01-01",
        "result_data": {
          "execution_id": "01234567-89ab-cdef-0123-456789abcdef",
          "query_id": 5399608,
          "is_execution_finished": true,
          "state": "QUERY_STATE_COMPLETED",
          "result": {
            "rows": [
              {
                "Contract Interaction": 150,
                "Date": "2024-01-01",
                "Users": 75
              }
            ],
            "metadata": {
              "column_names": ["Contract Interaction", "Date", "Users"],
              "row_count": 1,
              "datapoint_count": 3
            }
          }
        },
        "created_at": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

## 数据库结构

### dune_twitter_bindings 表
| 字段 | 类型 | 描述 |
|------|------|------|
| id | bigserial | 主键 |
| dune_query_id | varchar(255) | Dune 查询 ID |
| twitter_user_name | varchar(255) | Twitter 用户名 |
| chain_ids | jsonb | 支持的区块链 ID 列表 |
| project_name | varchar(500) | 项目名称 |
| project_logo | text | 项目 Logo URL |
| contract_addresses | jsonb | 合约地址列表 |
| tags | jsonb | 标签列表 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

### dune_query_results 表
| 字段 | 类型 | 描述 |
|------|------|------|
| id | bigserial | 主键 |
| dune_query_id | varchar(255) | Dune 查询 ID |
| query_date | date | 查询日期 |
| result_data | jsonb | 查询结果数据 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

## 配置

在 `service.toml` 文件中添加以下配置：

```toml
[dune]
# Enable Dune service (true/false)
enabled = true
# Dune API key (optional, for authenticated requests)
api_key = ""
# Dune API base URL
base_url = "https://api.dune.com"
# Request timeout in seconds
timeout_sec = 30
# Maximum number of retries for failed requests
max_retries = 3
# Delay between retries in seconds
retry_delay_sec = 1
```

## 定时任务

### 执行时间
- **频率**: 每天执行一次
- **时间**: UTC+8 上午 10:00
- **时区**: 使用 UTC+8 时区配置

### 执行逻辑
1. 获取所有唯一的 Dune 查询 ID
2. 检查当天是否已有数据，避免重复抓取
3. 调用 Dune API 获取最新数据
4. 将结果保存到数据库
5. 记录执行日志和错误信息

### 错误处理
- **重试机制**: 最多重试 3 次
- **延迟策略**: 递增延迟 (1s, 2s, 3s)
- **错误日志**: 详细记录失败原因
- **继续执行**: 单个查询失败不影响其他查询

## 项目信息自动获取

当创建新绑定时，如果未提供项目名称或 Logo，系统会自动调用现有的 API 获取：

1. **调用 getCaByUserName**: 根据 Twitter 用户名获取 CA 地址
2. **调用 getTokenInfo**: 根据 CA 地址获取代币信息
3. **提取项目信息**: 从代币信息中提取项目名称和 Logo

## 使用示例

### 1. 创建绑定关系
```bash
curl -X POST http://localhost:8080/api/dune/bindings \
  -H "Content-Type: application/json" \
  -d '{
    "dune_query_id": "5399608",
    "twitter_user_name": "example_user",
    "chain_ids": ["1", "56"]
  }'
```

### 2. 查询项目数据
```bash
curl "http://localhost:8080/api/dune/project-data?twitter_user_name=example_user&start_date=2024-01-01&end_date=2024-01-31"
```

### 3. 获取所有绑定
```bash
curl "http://localhost:8080/api/dune/bindings?limit=10&offset=0"
```

## 监控和日志

### 日志级别
- **INFO**: 正常执行流程和统计信息
- **DEBUG**: 详细的 API 请求信息
- **ERROR**: 错误情况和异常处理
- **WARN**: 警告信息（如项目信息获取失败）

### 关键指标
- 定时任务执行时间
- API 请求成功率
- 数据库操作性能
- 错误重试次数

## 故障排除

### 常见问题

1. **定时任务未执行**
   - 检查服务启动日志
   - 确认 Dune 服务已启用
   - 检查时区配置

2. **API 请求失败**
   - 检查网络连接
   - 验证 Dune API 状态
   - 检查 API 密钥配置

3. **数据库错误**
   - 确认数据库连接正常
   - 检查表结构是否正确
   - 验证数据格式

4. **项目信息获取失败**
   - 检查外部 API 可用性
   - 验证 Twitter 用户名有效性
   - 查看详细错误日志
